import React, { use<PERSON><PERSON>back, useMemo, useRef, useState, useContext, useEffect } from 'react'
import { allNodes, getNode } from '@repo/shared/lib/flow/registry'
import '@repo/shared/lib/flow/autoload'
import type { FlowGraph, NodeDefinition, NodeInputPort, NodeOutputPort } from '@repo/shared/lib/flow/types'
import { createShortUuid } from '@repo/shared/lib/utils'
import {
    Background,
    Panel,
    ReactFlow,
    ReactFlowProvider,
    addEdge,
    applyEdgeChanges,
    applyNodeChanges,
    Connection,
    Edge,
    EdgeChange,
    Node,
    NodeChange,
    NodeProps,
    Handle,
    Position,
    type NodeTypes,
    type EdgeTypes,
    ConnectionLineType,
    MarkerType,
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import EndLabelEdge from './EndLabelEdge'
import { Button } from '@repo/shared/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@repo/shared/components/ui/popover'
import { <PERSON>roll<PERSON><PERSON> } from '@repo/shared/components/ui/scroll-area'
import { MaximizeIcon, ZoomInIcon, ZoomOutIcon } from 'lucide-react'

import { produce } from 'immer'


export type RfNodeData = { nodeType: string; settings: any }

const FlowUpdateContext = React.createContext<{ updateNodeSettings: (nodeId: string, changes: Partial<any>) => void } | null>(null)

function RegistryNode(props: NodeProps) {
    const data = props.data as RfNodeData
    const ctx = useContext(FlowUpdateContext)
    const def = getNode(data.nodeType)
    const inputs = def?.inputs ? def.inputs((data.settings ?? {}) as any) : []
    const outputs = def?.outputs ? def.outputs((data.settings ?? {}) as any) : []

	return (
		<div className={`flex flex-col p-4 bg-background w-[430px] border-[1px] rounded-md ${props.selected ? 'border-primary' : 'border-border'}`}>
            {props.id}
			<div className="flex items-center gap-3">
				{/* <div className="p-2 rounded-lg bg-secondary/50">{def?.icon ?? null}</div> */}
				<h3 className="text-base font-medium text-foreground">{((data.settings ?? {}) as any).title || def?.label || data.nodeType}</h3>
			</div>

			<div className="text-xs text-muted-foreground mt-2">
				{def?.editor?.renderNodeBody
					? def.editor.renderNodeBody({
						node: {
							id: props.id,
							type: data.nodeType,
							position: { x: props.positionAbsoluteX, y: props.positionAbsoluteY },
							settings: data.settings,
						} as any,
					})
					: null}
			</div>
			<div
				className="text-xs text-muted-foreground nodrag nopan mt-2"
				onPointerDown={(e) => e.stopPropagation()}
				onMouseDown={(e) => e.stopPropagation()}
				onDoubleClick={(e) => e.stopPropagation()}
			>
				{def?.editor?.renderProperties ? def.editor.renderProperties({ settings: data.settings, onChange: (changes) => ctx?.updateNodeSettings(props.id, changes) }) : null}
			</div>

			{/* Input handles (targets) on the top */}
			{inputs.map((p, idx) => (
				<Handle
					key={p.key}
					id={p.key}
					type="target"
					position={Position.Top}
					style={{ left: `calc(50% + ${idx * 10}px)`, width: '10px', height: '10px'}}
				/>
			))}
			{/* Output handles (sources) on the bottom */}
			{outputs.map((p, idx) => (
				<Handle
					key={p.key}
					id={p.key}
					type="source"
					position={Position.Bottom}
					style={{ left: `calc(50% + ${idx * 10}px)`, width: '10px', height: '10px' }}
				/>
			))}
		</div>
	)
}

const nodeTypes: NodeTypes = { registryNode: RegistryNode }
const edgeTypes: EdgeTypes = { endLabel: EndLabelEdge }

//This is just for a page test!
export function FlowEditorNew() {
	const [graph, setGraph] = useState<FlowGraph>({ id: '1234', name: 'Untitled', nodes: [], edges: [] })


    return <div className='w-full h-[100dvh]'>

        <FlowEditor campaignFlow={graph} setFlow={setGraph}/>

    </div>
}

export function FlowEditor({ campaignFlow, setFlow }: { campaignFlow: FlowGraph, setFlow: (flow: FlowGraph) => void }) {

    const updateFlow = (updater: (flow: FlowGraph) => FlowGraph) => {
        const newFlow = updater(campaignFlow)
        setFlow(newFlow)
        console.log('newFlow', newFlow)
    }

    return <>
    <ReactFlowProvider><FlowEditorNewInternal graph={campaignFlow} setGraph={updateFlow} /></ReactFlowProvider>
    </>
}

function FlowEditorNewInternal({ graph, setGraph }: { graph: FlowGraph, setGraph: (updater: (graph: FlowGraph) => FlowGraph) => void }) {
    const palette = useMemo(() => allNodes(), [])


    const addNode = useCallback((def: NodeDefinition<any>) => {
        const id = createShortUuid()
        // Note: onCreate no longer receives customScreensContext since we're using declarative approach
        const partial = def.editor?.onCreate?.({ createId: () => createShortUuid(), customScreensContext: { addCustomScreen: () => {}, removeCustomScreen: () => {} } }) ?? {}
        const settings = (partial as any)?.settings ?? {}
        const position = { x: 100, y: 100 }
        const newNode = {
            id,
            type: 'registryNode',
            position,
            data: {
                nodeType: def.type,
                settings,
            } as RfNodeData,
        } as unknown as Node<RfNodeData>
        setGraph((g) => ({
            ...g,
            nodes: ([...((g as any).nodes ?? []), newNode] as any),
        }))
    }, [setGraph])

    const onNodesChange = useCallback((changes: NodeChange[]) => {
        setGraph((g) => {
            const nodes = (g as any).nodes as Node<RfNodeData>[]
            const filtered = (changes as any).filter((c: any) => {
                if (c.type !== 'remove') return true
                const node = nodes.find((n) => n.id === c.id)
                const deletable = Boolean(((node?.data as RfNodeData)?.settings ?? ({} as any)).deletable ?? true)
                return deletable
            }) as NodeChange[]
            return { ...g, nodes: applyNodeChanges(filtered as any, nodes as any) as any }
        })
    }, [setGraph])

    const onEdgesChange = useCallback((changes: EdgeChange[]) => {
        setGraph((g) => produce(g, (draft: any) => {
            const nodes = (draft as any).nodes as Node<RfNodeData>[]
            const edges = (draft as any).edges as Edge[]

            const updateNodeSettingsDraft = (nodeId: string, changes: Partial<any>) => {
                const n = nodes.find((n) => n.id === nodeId) as any
                if (!n) return
                n.data = { ...(n.data as any), settings: { ...(((n.data as any).settings ?? {}) as any), ...(changes ?? {}) } }
            }

            // Handle edge removals to call disconnection callbacks
            changes.forEach((change) => {
                if (change.type === 'remove') {
                    const removedEdge = edges.find((e) => e.id === change.id)
                    if (removedEdge) {
                        const sourceNode = nodes.find((n) => n.id === removedEdge.source)
                        const targetNode = nodes.find((n) => n.id === removedEdge.target)

                        if (sourceNode && targetNode) {
                            const sourceDef = getNode((sourceNode.data as RfNodeData).nodeType)
                            const targetDef = getNode((targetNode.data as RfNodeData).nodeType)

                            // Call onOutputDisconnected for the source node
                            sourceDef?.editor?.onOutputDisconnected?.({
                                graph: draft,
                                edge: removedEdge,
                                sourceNodeId: removedEdge.source!,
                                targetNodeId: removedEdge.target!,
                                customScreensContext,
                                updateNodeSettings: updateNodeSettingsDraft,
                            })

                            // Call onInputDisconnected for the target node
                            targetDef?.editor?.onInputDisconnected?.({
                                graph: draft,
                                edge: removedEdge,
                                sourceNodeId: removedEdge.source!,
                                targetNodeId: removedEdge.target!,
                                customScreensContext,
                                updateNodeSettings: updateNodeSettingsDraft,
                            })
                        }
                    }
                }
            })

            ;(draft as any).edges = applyEdgeChanges(changes as any, edges as any) as any
        }))
    }, [setGraph, customScreensContext])

    const onConnect = useCallback((connection: Connection) => {
        const hasAllHandles = Boolean(connection.source && connection.sourceHandle && connection.target && connection.targetHandle)
        if (!hasAllHandles) return
        setGraph((g) => produce(g, (draft: any) => {
            const nodes = (draft as any).nodes as Node<RfNodeData>[]
            const edges = (draft as any).edges as Edge[]
            const sourceNode = nodes.find((n) => n.id === connection.source)
            const targetNode = nodes.find((n) => n.id === connection.target)
            if (!sourceNode || !targetNode) return
            const sourceDef = getNode((sourceNode.data as RfNodeData).nodeType)
            const targetDef = getNode((targetNode.data as RfNodeData).nodeType)
            const sourceOutputs: NodeOutputPort[] = (sourceDef?.outputs ? sourceDef.outputs((((sourceNode.data as RfNodeData).settings ?? {}) as any)) : []) as NodeOutputPort[]
            const targetInputs: NodeInputPort[] = (targetDef?.inputs ? targetDef.inputs((((targetNode.data as RfNodeData).settings ?? {}) as any)) : []) as NodeInputPort[]
            const sourcePort = sourceOutputs.find((p) => p.key === connection.sourceHandle)
            const targetPort = targetInputs.find((p) => p.key === connection.targetHandle)
            const isCompatible = Boolean(sourcePort && targetPort && (!targetPort.acceptsOnly || targetPort.acceptsOnly.includes(sourcePort.kind)))
            if (!isCompatible) return
            const endLabel = sourcePort?.endLabel ?? sourcePort?.label
            const newEdge = { ...connection, type: 'endLabel', id: `e-${createShortUuid()}`, data: { endLabel } } as any
            const nextEdges = addEdge(newEdge, edges as any) as any

            // Mutator for editor callbacks that edits the same immer draft
            const updateNodeSettingsDraft = (nodeId: string, changes: Partial<any>) => {
                const n = nodes.find((n) => n.id === nodeId) as any
                if (!n) return
                n.data = { ...(n.data as any), settings: { ...(((n.data as any).settings ?? {}) as any), ...(changes ?? {}) } }
            }

            // Call connection callbacks on the draft graph
            sourceDef?.editor?.onOutputConnected?.({
                graph: { ...(draft as any), edges: nextEdges },
                edge: newEdge,
                sourceNodeId: connection.source!,
                targetNodeId: connection.target!,
                customScreensContext,
                updateNodeSettings: updateNodeSettingsDraft,
            })

            targetDef?.editor?.onInputConnected?.({
                graph: { ...(draft as any), edges: nextEdges },
                edge: newEdge,
                sourceNodeId: connection.source!,
                targetNodeId: connection.target!,
                customScreensContext,
                updateNodeSettings: updateNodeSettingsDraft,
            })

            ;(draft as any).edges = nextEdges
        }))
    }, [setGraph])

    const updateNodeSettings = useCallback((nodeId: string, changes: Partial<any>) => {
        console.log('updateNodeSettings', nodeId, changes)
        setGraph((g) => {

            const nodeToUpdate = (g as any).nodes.find((n: Node<RfNodeData>) => n.id === nodeId)
            if(!nodeToUpdate) return g

            const updatedNode = { ...nodeToUpdate, data: { ...(nodeToUpdate.data as RfNodeData), settings: { ...((nodeToUpdate.data as RfNodeData).settings ?? {}), ...(changes ?? {}) } } } as any
            const updatedGraph = { ...g, nodes: (g as any).nodes.map((n: Node<RfNodeData>) => n.id === nodeId ? updatedNode : n) }
            console.log('updatedGraph', updatedGraph)
            return updatedGraph

        })
    }, [setGraph])

	useEffect(() => {
        console.log('graph', graph)
		if(graph?.edges == null) {
			setGraph((g) => ({ ...g, edges: [] }))
		}
	}, [graph])

	return (
            <FlowUpdateContext.Provider value={{ updateNodeSettings }}>
				<ReactFlow
					proOptions={{ hideAttribution: true }}
					colorMode={'dark'}
					deleteKeyCode={'Delete'}
					nodes={graph.nodes ?? []}
					edges={graph.edges ?? [] }
					onNodesChange={onNodesChange}
					onEdgesChange={onEdgesChange}
					onConnect={onConnect}
					nodeTypes={nodeTypes}
					connectionLineType={ConnectionLineType.SmoothStep}
					defaultEdgeOptions={{
						type: 'endLabel',
						style: { stroke: '#2c91ed' },
						markerEnd: { type: MarkerType.ArrowClosed, color: '#2c91ed', width: 25, height: 25 },
					}}
					edgeTypes={edgeTypes}
					snapToGrid={true}
					snapGrid={[15, 15]}
					fitView
					fitViewOptions={{ maxZoom: 1.0 }}
					maxZoom={1.0}
					minZoom={0.7}
					panOnScroll={true}
					panOnDrag={[1, 2]}
					preventScrolling={true}
					translateExtent={[
						[-Infinity, -Infinity],
						[Infinity, Infinity],
					]}
				>
					<Background />
					<Panel position="top-right" className="flex gap-1 opacity-100">
						{/* Add Node popover */}
						<Popover>
							<PopoverTrigger asChild>
								<Button variant="secondary" className="bg-card hover:bg-accent/50 text-card-foreground">
									Add Node
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-80 p-0" align="start">
								<ScrollArea className="h-[400px]">
									<div className="p-2 grid gap-2">
										{palette.map((n) => (
											<Button key={n.type} variant="ghost" className="w-full justify-start" onClick={() => addNode(n)}>
												{n.label}
											</Button>
										))}
									</div>
								</ScrollArea>
							</PopoverContent>
						</Popover>

						<Button variant="ghost" className="bg-card text-card-foreground">
							<ZoomInIcon className="h-4 w-4" />
						</Button>
						<Button variant="ghost" className="bg-card text-card-foreground">
							<ZoomOutIcon className="h-4 w-4" />
						</Button>
						<Button variant="ghost" className="bg-card text-card-foreground">
							<MaximizeIcon className="h-4 w-4" />
						</Button>
					</Panel>
				</ReactFlow>
            </FlowUpdateContext.Provider>
	)
}

export default FlowEditorNew


