import type { NodeRuntimeContext, NodeRunResult, NodeChildRef } from '../../types'
import type { Settings } from './node.types'

export async function run({ ctx, children }: { ctx: NodeRuntimeContext; settings: Settings; children?: NodeChildRef[]; inputs?: Record<string, unknown> }): Promise<NodeRunResult> {
	// Use the node ID from context to find the specific reward screen node
	if (ctx.nodeId) {
		// Get the first input edge to determine screen ID
		const inputEdge = ctx.graph.edges.find(edge => edge.target === ctx.nodeId)
		if (inputEdge) {
			const screenId = "custom/outcome-" + inputEdge.id
			console.log("Open screen: ", screenId)
			ctx.setGameScene(ctx.graph.parentWidgetId, screenId)
		}
	}

	return { next: children }
}



