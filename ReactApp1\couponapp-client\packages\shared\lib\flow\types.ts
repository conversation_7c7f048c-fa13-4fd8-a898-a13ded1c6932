import type { Node as RFNode, Edge as RFEdge } from '@xyflow/react'
import { UseGameCustomScreensType } from '../types/gameCustomScreens'

export interface GameScreenDefinition {
    id: string
    name: string
    type: 'custom' | 'builtin'
}

export type PortKind = 'event' | 'data' | 'game-outcome'

export interface NodeInputPort {
    key: string
    label?: string
    /** Optional list of kinds this input accepts. If omitted, accepts all kinds. */
    acceptsOnly?: PortKind[]
    dataType?: string
}

export interface NodeOutputPort {
    key: string
    label?: string
    /** Kind produced by this output. */
    kind: PortKind
    dataType?: string
    /** Optional label to render at the end of edges originating from this output. */
    endLabel?: string
}

// React Flow node.data we store
export interface FlowNodeData<TSettings = any> extends Record<string, unknown> {
    nodeType: string
    settings?: TSettings
    additionalData?: Record<string, unknown>
}

export interface FlowGraph {
    id: string
    name: string
    // React Flow-native nodes/edges persistently stored
    nodes: RFNode<FlowNodeData>[]
    edges: RFEdge[]
    parentWidgetId?: string
}

export interface NodeChildRef {
    port: string
    nodeId: string
}

export interface NodeRunResult {
    // Id or ids of next node(s) to execute. If omitted or empty, execution stops.
    next?: NodeChildRef | NodeChildRef[]
}

export interface NodeRuntimeContext {
    graph: FlowGraph
    nodeId?: string
    cancelToken?: { isCancelled: () => boolean }
    setSceneById?: (sceneId: string) => void
    setGameScene?: (gameWidgetId: string, sceneId: string) => void
    payload?: any
}

export interface NodeConnectionContext {
    graph: FlowGraph
    edge: RFEdge
    sourceNodeId: string
    targetNodeId: string
    customScreensContext: UseGameCustomScreensType

    updateNodeSettings: (nodeId: string, changes: Partial<any>) => void
}

export interface NodeEditorContext {
    nodeId: string
    graph: FlowGraph
    inputEdges: RFEdge[]
    outputEdges: RFEdge[]
}

// Minimal shape passed to renderNodeBody/editor APIs, derived from RF node
export interface NodeEditorBodyProps<TSettings = any> {
    node: {
        id: string
        type: string
        position: { x: number; y: number }
        settings?: TSettings
    }
}

export interface NodeEditorPropertiesProps<TSettings = any> {
    settings: TSettings
    onChange: (changes: Partial<TSettings>) => void
}

export interface NodeDefinition<TSettings = any> {
    type: string
    label: string
    icon?: any
    inputs?: (settings: TSettings) => NodeInputPort[]
    outputs?: (settings: TSettings) => NodeOutputPort[]
    exposeScreens?: (settings: TSettings, ctx: NodeEditorContext) => GameScreenDefinition[]
    editor?: {
        renderNodeBody?: (props: NodeEditorBodyProps<TSettings>) => any
        renderProperties?: (props: NodeEditorPropertiesProps<TSettings>) => any
        onCreate?: (ctx: { createId: () => string; customScreensContext: UseGameCustomScreensType }) => Partial<{ settings: TSettings }> | void
        onDelete?: (ctx: { nodeId: string }) => void
        onInputConnected?: (ctx: NodeConnectionContext) => void
        onOutputConnected?: (ctx: NodeConnectionContext) => void
        onInputDisconnected?: (ctx: NodeConnectionContext) => void
        onOutputDisconnected?: (ctx: NodeConnectionContext) => void
    }
    runtime?: {
        onAttach?: (ctx: NodeRuntimeContext, settings: TSettings) => void | (() => void)
        onDetach?: () => void
        run: (args: { ctx: NodeRuntimeContext; settings: TSettings; inputs?: Record<string, unknown>; children?: NodeChildRef[] }) => Promise<NodeRunResult>
    }
}


