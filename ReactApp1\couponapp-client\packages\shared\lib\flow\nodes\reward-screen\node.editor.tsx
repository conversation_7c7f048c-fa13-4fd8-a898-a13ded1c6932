import React from 'react'
import { registerNode } from '../../registry'
import type { NodeDefinition, NodeEditorBodyProps, NodeEditorPropertiesProps, NodeEditorContext, GameScreenDefinition } from '../../types'
import type { Settings } from './node.types'
import { run } from './node.runtime'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'
import { Button } from '@repo/shared/components/ui/button'

function RewardScreenNodeBody(_: NodeEditorBodyProps) {
	return <div className="w-[260px]"></div>
}

function RewardScreenProperties({ settings, onChange }: NodeEditorPropertiesProps<Settings>) {
	return (
		<div className="space-y-2">
			<div className="space-y-1">
				<Label>Reward Set</Label>
				<Select value={settings?.rewardSetId ?? ''} onValueChange={(v) => onChange({ rewardSetId: v })}>
					<SelectTrigger>
						<SelectValue placeholder="Select reward set" /> 
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="default">Default</SelectItem>
						<SelectItem value="test">Test</SelectItem>
					</SelectContent>
				</Select>
			</div>
			<div className="pt-1">
				<Button variant="link" size="sm" className="px-0" onClick={() => {}}>
					Open in designer
				</Button>
			</div>
		</div>
	)
}

const def: NodeDefinition<Settings> = {
	type: 'client:RewardScreen',
	label: 'Game Reward Screen',
	icon: 'GiftIcon',
    inputs: () => [{ key: 'input',  acceptsOnly: ['game-outcome'] }],
	outputs: () => [{ key: 'output', kind: 'event' }],
	exposeScreens: (_settings: Settings, ctx: NodeEditorContext): GameScreenDefinition[] => {
		// Return screen definitions based on connected input edges
		const screens: GameScreenDefinition[] = []

		for (const edge of ctx.inputEdges) {
			const endLabel = (edge?.data as any)?.endLabel || 'Outcome'
			const screenId = "outcome-" + edge.id

			screens.push({
				id: screenId,
				name: "Outcome - " + endLabel,
				type: 'custom'
			})
		}

		return screens
	},
	editor: {
		renderNodeBody: RewardScreenNodeBody,
		renderProperties: RewardScreenProperties,
	},
	runtime: { run },
}

registerNode(def)
export default def



