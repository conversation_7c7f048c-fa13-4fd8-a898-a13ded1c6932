import { useMemo } from 'react'
import type { FlowGraph, GameScreenDefinition } from '../types'
import { getAllFlowGameScreens } from '../utils/graphAnalysis'

/**
 * Hook that scans a flow graph for nodes with exposeScreens method
 * and returns aggregated screen definitions, reactive to flow graph changes
 */
export function useFlowGameScreens(graph: FlowGraph | null): GameScreenDefinition[] {
    return useMemo(() => {
        if (!graph) return []
        
        try {
            return getAllFlowGameScreens(graph)
        } catch (error) {
            console.warn('Error getting flow game screens:', error)
            return []
        }
    }, [graph?.nodes, graph?.edges, graph?.id])
}
